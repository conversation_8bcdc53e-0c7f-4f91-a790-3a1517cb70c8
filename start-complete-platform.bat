@echo off
echo ========================================
echo Starting Healthy Diet Platform
echo ========================================

echo.
echo [Step 1/2] Starting Docker Infrastructure...
echo ========================================

echo Starting MySQL, Redis, Nacos containers...
docker-compose up -d

echo.
echo Waiting for infrastructure services to start...
timeout /t 5 /nobreak

echo.
echo Checking Docker service status:
docker-compose ps

echo.
echo [Step 2/3] Starting Microservices...
echo ========================================

echo.
echo Important Notes:
echo 1. Make sure all service configs are set in Nacos
echo 2. Nacos Console: http://localhost:8848/nacos (nacos/nacos)
echo 3. If first time, configure Nacos before continuing
echo.

set /p continue="Continue to start microservices? (Y/N): "
if /i "%continue%" neq "Y" (
    echo Microservice startup cancelled
    goto :end
)

echo.
echo Starting microservices...

echo.
echo [1/7] Starting Gateway Service (Port: 8080)...
cd /d "%~dp0health-backend\gateway-service"
start "Gateway Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [2/7] Starting Auth Service (Port: 8081)...
cd /d "%~dp0health-backend\auth-service"
start "Auth Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [3/7] Starting User Service (Port: 8082)...
cd /d "%~dp0health-backend\user-service"
start "User Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [4/7] Starting Recipe Service (Port: 8083)...
cd /d "%~dp0health-backend\recipe-service"
start "Recipe Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [5/7] Starting Plan Service (Port: 8084)...
cd /d "%~dp0health-backend\plan-service"
start "Plan Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [6/7] Starting Admin Service (Port: 8085)...
cd /d "%~dp0health-backend\admin-service"
start "Admin Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [7/7] Starting AIGC Service (Port: 8086)...
cd /d "%~dp0health-backend\aigc-service"
start "AIGC Service" cmd /k "mvn spring-boot:run"

echo.
echo ========================================
echo [Step 3/3] Starting Frontend...
echo ========================================

echo.
echo Starting Vue3 Frontend Application...
cd /d "%~dp0health-frontend"
start "Frontend" cmd /k "pnpm dev"

echo.
echo ========================================
echo Platform Startup Complete!
echo ========================================
echo.
echo Infrastructure Services:
echo - MySQL:    localhost:3307
echo - Redis:    localhost:6380
echo - Nacos:    localhost:8848
echo.
echo Microservices:
echo - Gateway:  localhost:8080
echo - Auth:     localhost:8081
echo - User:     localhost:8082
echo - Recipe:   localhost:8083
echo - Plan:     localhost:8084
echo - Admin:    localhost:8085
echo - AIGC:     localhost:8086
echo.
echo Frontend Application:
echo - Vue3 App: localhost:3001
echo.
echo Management UI:
echo - Nacos Console: http://localhost:8848/nacos
echo.
echo Please wait 2-3 minutes for all services to fully start...
echo.

:end
pause
