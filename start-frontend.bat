@echo off
echo ========================================
echo Starting Frontend Application
echo ========================================

echo.
echo Checking if dependencies are installed...
cd /d "%~dp0health-frontend"

if not exist "node_modules" (
    echo Installing dependencies...
    pnpm install
    echo.
)

echo Starting Vue3 Frontend...
start "Frontend" cmd /k "pnpm dev"

echo.
echo ========================================
echo Frontend Starting...
echo ========================================
echo.
echo Frontend will be available at:
echo http://localhost:3001
echo.
echo Vue DevTools available at:
echo http://localhost:3001/__devtools__/
echo.

pause
